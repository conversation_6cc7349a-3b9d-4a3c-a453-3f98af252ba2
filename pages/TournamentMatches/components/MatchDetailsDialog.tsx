import React from "react";
import { View, ScrollView } from "react-native";
import {
  Actionsheet,
  ActionsheetBackdrop,
  ActionsheetContent,
  ActionsheetDragIndicator,
  ActionsheetDragIndicatorWrapper,
} from "@/components/ui/actionsheet";
import { VStack } from "@/components/ui/vstack";
import { HStack } from "@/components/ui/hstack";
import { Text } from "@/components/ui/text";
import { Divider } from "@/components/ui/divider";
import { Match } from "../../../types/matches";
import { ParticipantDetails } from "../../../types/participants";
import ParticipantDisplay from "./ParticipantDisplay";
import dayjs from "dayjs";
import { CTAButton } from "@/components/ui/primaryCTAbutton";
import RenderIf from "@/components/util-components/RenderIf";

export interface MatchDetailsDialogProps {
  isOpen: boolean;
  onClose: () => void;
  match: Match;
  participant1Details?: ParticipantDetails | null;
  participant2Details?: ParticipantDetails | null;
}

const formatDateTime = (dateTime: string | null): string => {
  if (!dateTime) return "TBD";

  try {
    return dayjs(dateTime).format("MMM DD, YYYY h:mm A");
  } catch (error) {
    return "";
  }
};

const MatchDetailsDialog: React.FC<MatchDetailsDialogProps> = ({
  isOpen,
  onClose,
  match,
  participant1Details,
  participant2Details,
}) => {
  return (
    <Actionsheet isOpen={isOpen} onClose={onClose}>
      <ActionsheetBackdrop />
      <ActionsheetContent>
        <ActionsheetDragIndicatorWrapper>
          <ActionsheetDragIndicator />
        </ActionsheetDragIndicatorWrapper>

        <ScrollView
          className="w-full mt-3"
          showsVerticalScrollIndicator={false}
        >
          <VStack className="">
            {/* Participants Section */}
            <VStack className="space-y-4 p-4">
              <HStack className="items-center justify-between">
                <View className="flex-1 max-w-[45%]">
                  <ParticipantDisplay
                    participantId={match.participant_1_id}
                    participantType={match.participant_type}
                    participantName={match.participant_1_name}
                    fallbackName="TBD"
                    preloadedParticipant={participant1Details}
                  />
                </View>

                <View className="px-4">
                  <Text className="text-xl font-urbanistExtraBold text-gray-400 tracking-widest">
                    VS
                  </Text>
                </View>

                <View className="flex-1 max-w-[45%]">
                  <ParticipantDisplay
                    participantId={match.participant_2_id}
                    participantType={match.participant_type}
                    participantName={match.participant_2_name}
                    fallbackName="TBD"
                    preloadedParticipant={participant2Details}
                  />
                </View>
              </HStack>
            </VStack>
            <Divider className="w-1/2 items-center self-center" />
            <View className="flex flex-row gap-2 items-center space-x-3 self-center py-2 mb-6">
              <Text className="text-sm font-urbanistMedium text-gray-800">
                {formatDateTime(match.scheduled_date)}
              </Text>
              <RenderIf condition={!!match?.court_field_number}>
                <View className="w-1 h-1 bg-gray-500 rounded-full" />
                <Text className="text-sm font-urbanistMedium text-gray-800">
                  {match.court_field_number}
                </Text>
              </RenderIf>
            </View>

            <View className="border-t border-gray-300 border-dashed" />
            <VStack className="space-y-4 p-4 gap-2">
              <CTAButton title="Edit Match" onPress={onClose} />
              <CTAButton title="Cancel Match" onPress={onClose} />
            </VStack>
          </VStack>
        </ScrollView>
      </ActionsheetContent>
    </Actionsheet>
  );
};

export default MatchDetailsDialog;
